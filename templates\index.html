<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票咨询代理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <!-- Markdown-it for better Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.2/dist/markdown-it.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-chart-line"></i> 股票咨询代理系统</h1>
                <p class="subtitle">由多个专业代理组成的股票分析团队为您服务</p>
                <div class="team-info">
                    <span class="team-member"><i class="fas fa-user-tie"></i> Morgan (CEO)</span>
                    <span class="team-member"><i class="fas fa-search"></i> Leo (信息检索专家)</span>
                    <span class="team-member"><i class="fas fa-chart-bar"></i> Ken (题材挖掘专家)</span>
                    <span class="team-member"><i class="fas fa-money-bill-trend-up"></i> Lus (A股炒作大师)</span>
                    <span class="team-member"><i class="fas fa-file-alt"></i> Jess (秘书)</span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 侧边栏 -->
            <aside class="sidebar">
                <div class="control-panel">
                    <button id="initBtn" class="btn btn-primary">
                        <i class="fas fa-rocket"></i> 初始化代理
                    </button>
                    <div class="status-panel">
                        <h3><i class="fas fa-info-circle"></i> 系统状态</h3>
                        <div id="statusText" class="status-text">点击'初始化代理'开始</div>
                    </div>
                </div>

                <div class="help-panel">
                    <h3><i class="fas fa-lightbulb"></i> 使用提示</h3>
                    <ul class="help-list">
                        <li>首先点击"初始化代理"按钮</li>
                        <li>然后在聊天界面输入您想分析的题材</li>
                        <li>例如："请分析人工智能题材"</li>
                        <li>系统将自动协调多个专家进行分析</li>
                    </ul>
                </div>

                <div class="examples-panel">
                    <h3><i class="fas fa-star"></i> 示例问题</h3>
                    <div class="example-buttons">
                        <button class="example-btn" data-text="请分析人工智能题材">
                            <i class="fas fa-robot"></i> 人工智能题材
                        </button>
                        <button class="example-btn" data-text="分析新能源汽车板块">
                            <i class="fas fa-car-battery"></i> 新能源汽车
                        </button>
                        <button class="example-btn" data-text="半导体行业最新动态">
                            <i class="fas fa-microchip"></i> 半导体行业
                        </button>
                        <button class="example-btn" data-text="医药生物题材机会">
                            <i class="fas fa-pills"></i> 医药生物
                        </button>
                    </div>
                </div>
            </aside>

            <!-- 聊天区域 -->
            <section class="chat-section">
                <div class="chat-header">
                    <h2><i class="fas fa-comments"></i> 股票咨询聊天</h2>
                    <div class="connection-status" id="connectionStatus">
                        <i class="fas fa-circle"></i> 连接中...
                    </div>
                </div>

                <div class="chat-container">
                    <div id="chatMessages" class="chat-messages">
                        <div class="welcome-message">
                            <div class="welcome-content">
                                <i class="fas fa-hand-wave"></i>
                                <h3>欢迎使用股票咨询代理系统！</h3>
                                <p>请先点击左侧的"初始化代理"按钮，然后输入您想了解的股票题材或相关问题。</p>
                            </div>
                        </div>
                    </div>

                    <div class="chat-input-container">
                        <div class="input-wrapper">
                            <input type="text" id="messageInput" placeholder="请输入您想分析的题材或问题..." disabled>
                            <button id="sendBtn" class="send-btn" disabled>
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                        <div class="input-hint">
                            <i class="fas fa-info-circle"></i>
                            按 Enter 发送消息，Shift + Enter 换行
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer-content">
                <p><i class="fas fa-shield-alt"></i> 本系统仅供学习和研究使用，投资有风险，决策需谨慎</p>
                <div class="footer-links">
                    <span>Powered by Flask + WebSocket</span>
                    <span>|</span>
                    <span>AgentScope Multi-Agent System</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- 加载动画 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>正在初始化代理系统...</p>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notificationContainer" class="notification-container"></div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
